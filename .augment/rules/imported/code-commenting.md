---
type: "agent_requested"
---

# Правила комментирования кода для нейросети

Данный документ описывает правила оформления комментариев в коде, ориентированном на работу с нейросетями. Комментарии должны быть написаны в **безличной форме**, использовать **JSDoc** и избегать **неоднозначных местоимений**. Комментарии не должны содержать прямые упоминания вопросов вроде «почему», «какие последствия» или «что неочевидного происходит». Вместо этого требуется включать в текст объяснения, раскрывающие эти аспекты.

## Принципы написания комментариев

- **Ясность и обоснованность**  
  Каждый комментарий должен содержать:
  - Причину выполнения логики.
  - Возможное влияние на работу системы.
  - Разъяснение любых нестандартных или неочевидных решений.

- **Безличная форма**  
  Комментарии должны быть нейтральными, без использования местоимений: «мы», «я», «он» и т.п. Следует использовать формулировки вроде «Выполняется проверка...», «Реализовано сравнение...».

- **JSDoc для функций и компонентов**  
  Все функции, компоненты и важные участки логики должны сопровождаться JSDoc-комментарием с описанием назначения, параметров и возвращаемого значения.

- **Избегание очевидных комментариев**  
  Не нужно описывать то, что полностью ясно из кода (например, `// Устанавливает значение true`, если написано `flag = true`).