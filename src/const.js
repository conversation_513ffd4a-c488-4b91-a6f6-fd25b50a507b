const AppRoute = {
  ROOT: '/',
  LINKS: '/links',
  AUTH: '/auth/login',
  REGISTER: '/auth/register',
  PROMOCODES: '/promocodes',
  PROMOCODE_ORDERS: '/promocode',
  PROMOCODE_SCREEN: '/promocode/:code',
  EVENTS: '/events',
  EVENT_SCREEN: '/events/:publicId',
  ORDERS: '/orders',
  ORDER_INFO_SCREEN: '/orders/:public_id',
  ORDER_INFO_START_NUMBER: '/orders/:public_id/:start_number',
  NEWS: '/news',
  NEWS_EDITOR: '/news/editor',
  NEWS_EDITOR_STORY: '/news/editor/:public_id',
  USERS: '/users',
  USER_SCREEN: '/user/:id',
  USER_PROFILE: '/user',
  PROFILE: '/profile',
  CUSTOM_ORDER: '/custom-order',
  CREATE_CUSTOM_ORDER: '/custom-order/create',
  CORP: '/corp',
  COMPANY: '/corp/company/:public_id',
  CORP_FORM: '/corp/form',
  CORP_FORM_ITEM: '/corp/form/:public_id',
  SUPPLIER: '/supplier',
  CREATE_SUPPLIER_SCREEN: '/create-supplier',
  ONLINE_RESULTS: '/online-results',
  RESULTS: '/results',
  RESULTS_TABLE: '/results/table/:id',
  RESULTS_TABLE_EDIT: '/results/table/edit/:id',
  RESULTS_CREATE: '/results/create',
  RESULTS_EDIT: '/results/edit/:publicId',
  SHOP: '/shop',
  SHOP_FORM: '/shop/form',
  SHOP_FORM_ITEM: '/shop/form/:public_id',
  ATHLETES: '/athletes',
  ATHLETES_PROFILE: '/athletes/:public_id',
  COACHES_PROFILE: '/coaches/:public_id',
  TEAMS: '/teams/:public_id/:title',
  PAGES: '/pages',
  PAGES_FORM: '/pages/form',
  PAGES_FORM_ITEM: '/pages/form/:public_id',
  LICENCES: '/licences',
  LICENCES_FORM_ITEM: '/licences/form/:public_id',
  LICENCES_TYPES_FORM: '/licences/form-types',
  LICENCES_TYPES_FORM_ITEM: '/licences/form-types/:public_id',
  DOCUMENTS: '/documents',
  DOCUMENTS_FORM: '/documents/form',
  BANNERS: '/banners',
  BANNERS_FORM: '/banners/form',
  BANNERS_FORM_ITEM: '/banners/form/:banner_type/:public_id',
  CALENDAR: '/calendar',
  SETTINGS: '/settings',
  SETTINGS_FORM: '/settings/form',
  SETTINGS_FORM_ITEM: '/settings/form/:public_id',
  CLUBS: '/clubs',
  CLUB: '/clubs/club/:public_id',
  BRANCHES: '/branches',
  MANAGEMENT: '/management',
  INSURANCES: '/insurances',
  INSURANCES_TAB: '/insurances/:tab',
  INSURANCE_TYPE_CREATE: '/insurances/types/create',
  INSURANCE_TYPE_EDIT: '/insurances/types/edit',
  INSURANCE_TYPE_EDIT_ID: '/insurances/types/edit/:public_id',
  OLYMPIC_RESERVE: '/olympic',
  OFFLINE: '/offline',
  EVENTS_CREATE: '/events/create',
  EVENTS_EDIT: '/events/edit',
  EVENTS_FORM_ITEM: '/events/edit/:eventPublicId',
  CITY_FORMATS: '/city-formats/:eventPublicId/:cityPublicId',
  CITY_FORMATS_STANDALONE: '/city-formats/:cityPublicId',
  CITY_FORMAT: '/city-format/:publicId',
  PROCESSING: '/processing',
  PROCESSING_CREATE: '/processing/create',
  PROCESSING_EDIT: '/processing/edit',
  PROCESSING_EDIT_ITEM: '/processing/edit/:public_id',
  TICKET: '/ticket/:id',
  SOLDGOOD: '/soldgood/:id',
}

const APIRoute = {
  GET_LINKS: '/api/click/list',
  EVENT_CLUSTER_STATISTICS: '/api/admin/cluster/statistics',
  CORP_TEAM: '/api/team',
  GET_CORP_TEAMS: '/api/admin/corporate/team',
  ASSIGN_CORP_TEAMS: '/api/admin/corporate/team/order/soldgoods',
  CREATE_LINK: '/api/click',
  SEND_RESULTS: '/api/admin/event/csv/results',
  SEND_RESULTS_EXCEL: '/api/admin/event/xlsx/results',
  LOGIN: '/api/auth',
  GET_ONE_PROMO: '/api/search/promocodes',
  GET_PROMO_GOODS: '/api/shop/public/preview?limit=10000',
  STAT_USAGE_PROMO: '/api/admin/promocode/usage/statistics',
  REGISTER: '/api/user',
  USER_INFO: '/api/user/info' /* /{user_public_id} */,
  USER_LIST: '/api/user/list' /* /{skip}/{limit} */,
  USERS_ROLE: '/api/role/users',
  USER_TICKETS: '/api/user/ticket/list' /* /{user_public_id} */,
  USER_ORDERS: '/api/user/order/list' /* /{user_public_id} */,
  USER_SOLDGOODS: '/api/user/all/soldgoods' /* /{user_public_id} */,
  USER_AUTH_HISTORY: '/api/user/auth/history' /* /{public_id} */,
  SEARCH_USER: '/api/search/user',
  CUD_USER: '/api/superadmin/user' /* /{public_id} */,
  SUPERADMIN_ROLE: '/api/superadmin/role' /* /{public_id} */,
  GET_NEWS: '/api/admin/news',
  POST_NEWS: '/api/admin/news',
  DELETE_NEWS: '/api/admin/news',
  UPDATE_NEWS: '/api/admin/news',
  GET_SOLD_GOODS: '/api/admin/soldgoods/excel',
  GET_SOLD_GOODS_ORGEO: '/api/manager/members/csv_orgeo',
  GET_SOLD_GOODS_JSON: '/api/admin/soldgoods/json',
  GET_PROMOCODE_ORDERS: '/api/promocode/orders',
  CHANGE_USER_TICKET: '/api/admin/change/user/ticket',
  CHANGE_USER_ORDER: '/api/change/user/order',
  CLOSE_ORDER_FISCAL: '/api/admin/close/fiscal/format/order',
  GENERATE_NEW_TICKET_NUMBER: '/api/admin/generate/ticket/number',
  EDIT_TICKET_DATA: '/api/volunteer/ticket/offline/register',
  CHANGE_RESULT_TICKET_NUMBER: '/api/admin/user/ticket/result',
  GET_EVENT: '/api/admin/event',
  EVENT_LIST: '/api/admin/event/list',
  EVENT_ARCHIVE: '/api/event/archive',
  EVENT_FORMATS: '/api/admin/event_format/event' /* /{event_public_id}/{city_id} */,
  EVENT_FORMAT_LIST: '/api/admin/event_format/list' /* /{event_public_id}/ */,
  CU_EVENT_CITY_FORMAT: '/api/admin/event_format' /* /{public_id}/ */,
  DELETE_EVENT_CITY_FORMAT: '/api/admin/delete/event_format',
  EVENT_ADMIN_FORMATS: '/api/admin/formats' /* /{event_public_id}/{event_city_id} */,
  EVENT_FORMAT: '/api/admin/event_format' /* /{public_id} */,
  EVENT_LOCATION_STATISTICS: '/api/admin/location/statistics' /* /{event_city_public_id}/ */,
  EVENT_UNIQUE_USERS_STATISTICS: '/api/statistics/events/unique_users',
  EVENT_UNIQUE_USERS_XLSX: '/api/xlsx/statistics/event/unique_users',
  CU_EVENT: '/api/admin/event' /* /{public_id}/ */,
  CUD_EVENT_CITY: '/api/admin/event_city' /* /{public_id}/ */,
  GET_EVENT_CITIES: '/api/admin/event_city/list' /* /{event_public_id}/ */,
  CLONE_EVENT_CITY: '/api/admin/clone/event/city',
  CHANGE_TICKET_FORMAT: '/api/admin/change/ticket/format',
  DELETE_TICKET: '/api/admin/delete/ticket',
  TICKETS: '/api/admin/tickets' /* /{order_public_id} */,
  GET_VOLUNTEER_SOLDGOOD: '/api/volunteer/soldgood' /* /{public_id} */,
  CHANGE_CUSTOM_TICKET_NUMBER: '/api/admin/assign/ticket/number',
  GENERATE_PROMOCODE: '/api/generate/promocode',
  GENERATE_PROMOCODES: '/api/generate/promocodes',
  PROMOCODE_LIST: '/api/promocode' /* /{skip}/{limit} */,
  PROMOCODE: '/api/admin/promocode',
  PROMOCODE_CHECK: '/api/promocode/check',
  PROMOCODES_XLSX: '/api/payment/promocode/statistics/xlsx' /* /{event_public_id} */,
  PROMOCODE_TICKETS_XLSX: '/api/admin/xlsx/promocode/soldgoods' /* /{promocode} */,
  SHIRTS_XLSX: '/api/admin/xlsx/shirts/statistics' /* /{event_public_id} */,
  CREATE_CUSTOM_ORDER: '/api/random/payment',
  CUSTOM_ORDERS: '/api/other/orders',
  DELETE_ORDER: '/api/admin/delete/order',
  DATES_TICKETS: '/api/admin/dates/tickets',
  REFUND_DATES_TICKETS: '/api/admin/refund/dates/tickets',
  GET_EXCEL_DATES_TICKETS: '/api/admin/dates/event_city/tickets',
  GET_EXCEL_REFUND_DATES_TICKETS: '/api/admin/refund/dates/event_city/tickets',
  ORDERS_EMAIL: '/api/admin/orders/email' /* /{public_id} */,
  SOLDGOODS_EXCEL: '/api/admin/soldgoods/excel' /* /{event_city.public_id} */,
  SOLDGOODS_EVENT_EXCEL: '/api/admin/soldgoods/event/excel' /* /{event_public_id} */,
  ORDER_INFO: '/api/info/order' /* /{public_id} */,
  ORDER_INFO_TICKET: '/api/admin/order' /* {format_public_id}/{ticket_number} */,
  CORP_ORDERS: '/api/admin/corporate/orders',
  CORP_ORDERS_SEARCH: '/api/admin/search/order',
  CREATE_PAYMENT: '/api/admin/payment',
  SUPPLIER: '/api/admin/uniteller/agent',
  ONLINE_RESULTS: '/api/admin/online/tickets' /* {event_public_id} */,
  RESULTS: '/api/admin/event/results',
  RESULTS_LIST: '/api/admin/results',
  RESULTS_LIST_EVENT_TYPE: '/api/admin/results/event_type',
  RESULTS_EDIT: '/api/admin/results/settings',
  RESULT_EDIT_PUBLIC_ID: '/api/admin/result',
  RESULTS_UPDATE: '/api/admin/cities/event/results',
  MATCHING_RESULTS: '/api/results/result/formats',
  ADDITIONAL_RESULTS: '/api/admin/event/file/additional/results',
  SHOP_ITEMS: '/api/admin/shop/list',
  SHOP_ORDERS: '/api/admin/dates/product/soldgoods',
  DATES_SOLDGOODS_XLSX: '/api/admin/xlsx/dates/product/soldgoods',
  DATES_PRODUCT: '/api/admin/dates/products',
  SHOP_STATISTICS: '/api/payment/shop/statistics',
  SHOP_CATEGORIES: '/api/admin/shop/category',
  SHOP_PRODUCT: '/api/admin/product/item',
  SHOP_SHIRTS: '/api/shop/shirts' /* GET /{event_city_public_id}/{event_public_id} */,
  BOUGHT_PRODUCTS: '/api/admin/bought/products',
  PRODUCT_PROPORTION: '/api/admin/product/proportion',
  PRODUCT_PROPORTION_LIST: '/api/admin/product/proportion/list',
  ATHLETES_GET_ALL: '/api/members/kind/athletes',
  API_MEMBER: '/api/members/member',
  GROUP_MEMBERS: '/api/members/group',
  GET_REGION_RU: '/api/location/region/list/RU',
  GET_ALL_COACHES: '/api/members/kind/coaches',
  GET_PAGES: '/api/admin/pages',
  ACTIONS_PAGE: '/api/admin/page' /* POST, PUT, DELETE */,
  FILES: '/api/files/list' /* {skip}/{limit} */,
  FILES_USER: '/api/files' /* {user_public_id} */,
  ACTIONS_FILE: '/api/file' /* POST, (GET, DELETE /{public_id}) */,
  FILES_DOCUMENT: '/api/files/document',
  ACTIONS_BANNERS: '/api/admin/banner' /* GET, POST, PUT, DELETE */,
  BANNERS_PAGE: '/api/admin/page/banner' /* /{page} */,
  ACTIONS_COLLECTION: '/api/admin/shop/collection' /* GET, POST, (PUT, DELETE /{public_id}) */,
  ACTIONS_EVENT_TYPE: '/api/admin/event_type' /* GET, POST, DELETE, (PUT, GET /{public_id}) */,
  EVENT_TYPE_LIST: '/api/admin/event_type',
  EVENT_CITY_FILTERED: '/api/admin/event_city/filtered',
  GET_CALENDAR_LIST: '/api/admin/calendar/list',
  ACTIONS_CALENDAR: '/api/calendar' /* POST, (PUT, DELETE /{public_id}) */,
  ACTIONS_SETTINGS: '/api/admin/communication/settings' /* GET, POST (PUT /{name}/{type}) */,
  GET_SECTIONS_LIST: '/api/admin/files/section',
  GET_DELIVERY_LIST: '/api/admin/delivery',
  GET_CLUBS: '/api/admin/clubs',
  GET_CLUB_STATISTICS: '/api/admin/club/statistics' /* {public_id} */,
  GET_CLUB_USERS: '/api/admin/club/users' /* {public_id} */,
  ACTIONS_CLUB: '/api/admin/club',
  CUD_COMPETITIONS_TYPE: '/api/admin/competition/type',
  GET_COMPETITIONS_TYPE: '/api/list/competition/type',
  GET_CLUB_COMPETITIONS: '/api/admin/club/competition/list' /* /{club_public_id} */,
  GET_CLUB_SOLDGOODS_STATISTICS: '/api/admin/club/soldgoods/statistics' /* /{event_city_public_id} */,
  CLUB_JOIN_STATISTICS: '/api/admin/club/join/dates/statistics',
  UD_CLUB_COMPETITION: '/api/admin/user/competition' /* /{public_id} */,
  BRANCHES_LIST: '/api/branches/list',
  CUD_BRANCHES: '/api/branches',
  GET_COMPANIES: '/api/companies',
  GET_COMPANY: '/api/company' /* /{public_id} */,
  GET_COMPANY_ORDERS: '/api/admin/search/corporate/order',
  CUD_COMPANY: '/api/admin/company',
  SOLDGOOD_REFUND: '/api/admin/refund/soldgood',
  SOLDGOOD_FORCED_REFUND: '/api/admin/forced/refund/soldgood',
  SOLDGOOD_SEARCH: '/api/admin/search/soldgood',
  UPLOAD_MEMBERS_ORDER: '/api/admin/members/order',
  EDIT_TEAM_TICKET: '/api/team/transfer/ticket',
  FORMAT_TEAMS: '/api/teams' /* /{format_public_id} */,
  INSIDE_TEAM_NUMBERS: '/api/volunteer/inside/team/numbers' /* /{public_id} */,
  REMOVE_TEAM_TICKET: '/api/admin/team/ticket/',
  RESULTS_FORMAT: '/api/admin/results/format',
  RESULTS_FORMAT_EVENT: '/api/event_results' /* /{external_format}/{event_city_public_id} */,
  CRUD_ADMIN_MANAGEMENT: '/api/admin/management' /* /{public_id} */,
  GET_MANAGEMENTS: '/api/admin/managements',
  GET_STATISTICS_INSURANCE: '/api/admin/insurance/buy/statistics',
  SOLDGOOD_INSURANCES: '/api/admin/soldgood/insurances' /* POST */,
  UPDATE_INSURANCE: '/api/admin/soldgood/insurance' /* PUT /{public_id} */,
  GET_TYPES_INSURANCE: '/api/admin/insurances',
  ACTIONS_TYPES_INSURANCE: '/api/admin/insurance' /* POST, (PUT, DELETE /{public_id}) */,
  INSURANCE_FORMATS: '/api/insurances/event/formats' /* /{public_id} */,
  SEARCH_INSURANCE_NUMBER: '/api/admin/insurance/number/soldgoods' /* /{number} */,
  CRUD_INSURANCE_INTEGRATIONS: '/api/admin/insurance/api-client' /* /{public_id} */,
  INSURANCE_API_CLIENT_SOLDGOODS: '/api/admin/api-client/soldgoods',
  GET_OLYMPIC_FEEDBACK: '/api/admin/olympic/feedback',
  GET_OLYMPIC_APPLICATION: '/api/admin/olympic/application',
  CLUB_COMPETITION_XLSX: '/api/admin/xlsx/club/competition' /* /{club_public_id} */,
  CLUB_USERS_XLSX: '/api/admin/club/xlsx/users' /* /{club_public_id} */,
  CLUB_SOLDGOODS_XLSX: '/api/admin/club/xlsx/soldgoods/statistics' /* /{event_city_public_id} */,
  CLUB_LIST_XLSX: '/api/admin/clubs/xlsx',
  SYNC_OFFLINE_USERS: '/api/admin/offline/users',
  SYNC_OFFLINE_EVENTS: '/api/admin/offline/events',
  ADMIN_RASPBERRY: '/api/admin/raspberry/offline/tickets' /* /{event_city_public_id} */,
  GET_COUNTRY_CITIES: '/api/location/city/list' /* {country_iso} */,
  GET_COUNTRY_REGIONS: '/api/location/region/list' /* {country_iso} */,
  GET_REGION_CITIES: '/api/location/city/region' /* {region_id} */,
  GET_PROCESSING_LIST: '/api/admin/processing/list',
  CUD_PROCESSING: '/api/admin/processing' /* /{public_id} */,
  GENERATE_TEAMS: '/api/admin/generate/teams',
  COMPLETE_TEAMS: '/api/admin/format/teams/complete',
  SPORT_TYPE: '/api/admin/sport/type',
  SPORT_TYPE_LIST: '/api/admin/sport/type/list',
  COUNTRY_LIST: '/api/location/country/list',
  SHOP_HONEST_CODE: '/api/admin/shop/honest_code',
  SHOP_PROPORTION_HONEST_CODES: '/api/admin/shop/proportion',
  EVENT_CITIES_UNIQUE_USERS: '/api/statistics/event_cities/unique_users',
  EVENT_CITIES_UNIQUE_USERS_XLSX: '/api/xlsx/statistics/event_city/unique_users',
  EVENT_TICKET_STATISTICS: '/api/admin/events/ticket/statistics',
  EVENT_TICKET_STATISTICS_XLSX: '/api/admin/xlsx/events/ticket/statistics',
  USER_DATE_STATISTICS: '/api/admin/date/stats/users',
  USER_DATE_STATISTICS_XLSX: '/api/admin/dates/stats/xlsx/users',
}

const AuthorizationStatus = {
  AUTH: 'AUTH',
  NO_AUTH: 'NO_AUTH',
  UNKNOWN: 'UNKNOWN',
}

const Gender = {
  MALE: 'male',
  FEMALE: 'female',
}

const Role = {
  USER: 'user',
  ADMIN: 'admin',
  SUPERADMIN: 'superadmin',
  PROMO: 'promo',
  ORDER: 'order',
  BANNERS: 'banners',
  BRANCHES: 'branches',
  CALENDAR: 'calendar',
  COMPANY: 'company',
  CLICKER: 'clicker',
  CLUBS: 'clubs',
  EVENTS: 'events',
  EVENT_TYPE: 'event_type',
  MEMBERS: 'members',
  PAGES: 'pages',
  NEWS: 'news',
  USERS: 'users',
  RESULTS: 'results',
  ONLINE: 'online',
  SHOP: 'shop',
  COMMUNICATION: 'communication',
  OLYMPIC: 'olympic',
  DOCUMENT: 'document',
  MANAGEMENT: 'management',
  INSURANCE: 'insurance',
  VOLUNTEER: 'volunteer',
  SUPERVOLUNTEER: 'supervolunteer',
  CREATE_ORDER: 'create_order',
  SUPPORT: 'support',
  OFFLINE: 'offline',
  XLSX_TICKETS: 'xlsx_tickets',
  XLSX_SHIRTS: 'xlsx_shirts',
  PROCESSING: 'processing',
  EVENTS_LIMITED: 'events_limited',
}

const Kind = {
  ATHLETE: 'athlete',
  CORPORATE: 'corporate',
  PARTNER: 'partner',
  BRAND: 'brand',
  SPORT: 'sport',
  OTHER: 'other',
}

const MB_IN_B = 1048576

const MAX_IMG_SIZE = MB_IN_B * 5

const MILLISECONDS_IN_SECOND = 1000
const SECONDS_IN_MINUTE = 60
const SECONDS_IN_HOUR = 3600
const HOURS_IN_DAY = 24

export {
  Role,
  Kind,
  Gender,
  MB_IN_B,
  AppRoute,
  APIRoute,
  MAX_IMG_SIZE,
  SECONDS_IN_MINUTE,
  AuthorizationStatus,
  MILLISECONDS_IN_SECOND,
  SECONDS_IN_HOUR,
  HOURS_IN_DAY,
}
