import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, Badge, ListGroup, Modal } from 'react-bootstrap'

/**
 * Modal component for selecting events
 */
const EventSelectionModal = ({ show, onHide, events, title, onSelectEvent, selectedEventIds }) => {
  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {events.length === 0 ? (
          <p className="text-muted">Нет доступных событий</p>
        ) : (
          <ListGroup>
            {events.map((event) => {
              const isSelected = selectedEventIds.includes(event.public_id)
              return (
                <ListGroup.Item
                  key={event.public_id}
                  action
                  onClick={() => !isSelected && onSelectEvent(event)}
                  disabled={isSelected}
                  className={isSelected ? 'bg-light text-muted' : ''}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <strong>{event.title}</strong>
                      <br />
                      <small className="text-muted">ID: {event.public_id}</small>
                    </div>
                    {isSelected && <Badge bg="success">Выбрано</Badge>}
                  </div>
                </ListGroup.Item>
              )
            })}
          </ListGroup>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Закрыть
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

/**
 * Component for selecting events for statistics
 */
const EventSelector = ({ activeEvents, archiveEvents, selectedEvents, onAddEvent, onRemoveEvent }) => {
  const [showActiveModal, setShowActiveModal] = useState(false)
  const [showArchiveModal, setShowArchiveModal] = useState(false)

  const selectedEventIds = selectedEvents.map((event) => event.public_id)

  const handleSelectActiveEvent = (event) => {
    onAddEvent(event, false)
    setShowActiveModal(false)
  }

  const handleSelectArchiveEvent = (event) => {
    onAddEvent(event, true)
    setShowArchiveModal(false)
  }

  return (
    <div>
      {/* Action Buttons */}
      <Row className="mb-3">
        <Col md={6}>
          <Button variant="outline-primary" onClick={() => setShowActiveModal(true)} className="w-100">
            Добавить актуальное событие
          </Button>
        </Col>
        <Col md={6}>
          <Button variant="outline-secondary" onClick={() => setShowArchiveModal(true)} className="w-100">
            Добавить архивное событие
          </Button>
        </Col>
      </Row>

      {/* Selected Events List */}
      {selectedEvents.length > 0 && (
        <div>
          <h6>Выбранные события ({selectedEvents.length}):</h6>
          <ListGroup className="mb-3">
            {selectedEvents.map((event) => (
              <ListGroup.Item key={event.public_id} className="d-flex justify-content-between align-items-center">
                <div>
                  <strong>{event.title}</strong>
                  <span className="text-muted"> ({event.public_id})</span>
                  {event.isArchive && (
                    <Badge bg="secondary" className="ms-2">
                      Архив
                    </Badge>
                  )}
                </div>
                <Button variant="outline-danger" size="sm" onClick={() => onRemoveEvent(event.public_id)}>
                  Удалить
                </Button>
              </ListGroup.Item>
            ))}
          </ListGroup>
        </div>
      )}

      {selectedEvents.length === 0 && (
        <div className="text-center text-muted p-3 border rounded">
          <p className="mb-0">Выберите события для получения статистики</p>
        </div>
      )}

      {/* Modals */}
      <EventSelectionModal
        show={showActiveModal}
        onHide={() => setShowActiveModal(false)}
        events={activeEvents}
        title="Выбрать актуальное событие"
        onSelectEvent={handleSelectActiveEvent}
        selectedEventIds={selectedEventIds}
      />

      <EventSelectionModal
        show={showArchiveModal}
        onHide={() => setShowArchiveModal(false)}
        events={archiveEvents}
        title="Выбрать архивное событие"
        onSelectEvent={handleSelectArchiveEvent}
        selectedEventIds={selectedEventIds}
      />
    </div>
  )
}

export default EventSelector
