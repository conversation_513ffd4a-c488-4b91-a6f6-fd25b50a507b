import { Table } from 'react-bootstrap'

/**
 * Utility function to format numbers with thousands separator
 */
const formatNumber = (number) => {
  if (number === null || number === undefined) return '0'
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')
}

/**
 * Component for displaying statistics table
 */
const StatisticsTable = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center text-muted p-4">
        <p>Нет данных для отображения</p>
      </div>
    )
  }

  return (
    <div className="table-responsive">
      <Table striped bordered hover>
        <thead className="table-dark">
          <tr>
            <th>Название</th>
            <th>Всего</th>
            <th>Физики</th>
            <th>Корпы</th>
            <th>Партнеры</th>
            <th>Бренд</th>
            <th>Спорт</th>
            <th>Другие</th>
            <th>Физики деньги</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={item.event_public_id || index}>
              <td>
                <strong>{item.event_title}</strong>
                <br />
                <small className="text-muted">({item.event_public_id})</small>
              </td>
              <td className="text-end">{formatNumber(item.total)}</td>
              <td className="text-end">{formatNumber(item.athlete)}</td>
              <td className="text-end">{formatNumber(item.corporate)}</td>
              <td className="text-end">{formatNumber(item.partner)}</td>
              <td className="text-end">{formatNumber(item.brand)}</td>
              <td className="text-end">{formatNumber(item.sport)}</td>
              <td className="text-end">{formatNumber(item.other)}</td>
              <td className="text-end">{formatNumber(item.athlete_cost_total)}</td>
            </tr>
          ))}
        </tbody>
        {data.length > 1 && (
          <tfoot className="table-secondary">
            <tr>
              <th>Итого</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.total || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.athlete || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.corporate || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.partner || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.brand || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.sport || 0), 0))}</th>
              <th className="text-end">{formatNumber(data.reduce((sum, item) => sum + (item.other || 0), 0))}</th>
              <th className="text-end">
                {formatNumber(data.reduce((sum, item) => sum + (item.athlete_cost_total || 0), 0))}
              </th>
            </tr>
          </tfoot>
        )}
      </Table>
    </div>
  )
}

export default StatisticsTable
