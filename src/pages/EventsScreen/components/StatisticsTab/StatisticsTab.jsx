import { useState, useCallback } from 'react'
import { <PERSON><PERSON>, Col, Row, Container, Table, Alert, Card } from 'react-bootstrap'

import Loader from '@/components/Loader/Loader'

import { useDownloadEventTicketStatisticsXlsx } from '@/features/events/api/downloadEventTicketStatisticsXlsx'
import { useGetEventArchive } from '@/features/events/api/getEventArchive'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetEventTicketStatistics } from '@/features/events/api/getEventTicketStatistics'
import { useToast } from '@/hooks/useToast'

import EventSelector from './components/EventSelector/EventSelector'
import StatisticsTable from './components/StatisticsTable/StatisticsTable'

/**
 * Custom hook to manage statistics data
 */
const useStatisticsData = () => {
  const [selectedEvents, setSelectedEvents] = useState([])
  const [statisticsData, setStatisticsData] = useState(null)

  const openToast = useToast()
  const { data: activeEventsData, isLoading: activeEventsLoading } = useGetEvents()
  const { data: archiveEventsData, isLoading: archiveEventsLoading } = useGetEventArchive({})

  const { mutate: getStatistics, isLoading: statisticsLoading, error: statisticsError } = useGetEventTicketStatistics()
  const { mutate: downloadStatistics, isLoading: downloadLoading } = useDownloadEventTicketStatisticsXlsx()

  const activeEvents = activeEventsData?.data?.values || []
  const archiveEvents = archiveEventsData?.data?.values || []

  const handleAddEvent = useCallback((event, isArchive = false) => {
    const eventWithType = { ...event, isArchive }
    setSelectedEvents((prev) => {
      // Проверяем, не добавлен ли уже этот event
      const exists = prev.some((e) => e.public_id === event.public_id)
      if (exists) return prev
      return [...prev, eventWithType]
    })
  }, [])

  const handleRemoveEvent = useCallback((publicId) => {
    setSelectedEvents((prev) => prev.filter((e) => e.public_id !== publicId))
  }, [])

  const handleGetStatistics = useCallback(() => {
    if (selectedEvents.length === 0) return

    const eventPublicIdList = selectedEvents.map((event) => event.public_id)

    getStatistics(
      { event_public_id_list: eventPublicIdList },
      {
        onSuccess: (response) => {
          if (response?.data?.values) {
            setStatisticsData(response.data.values)
            openToast.success({ message: 'Статистика билетов успешно загружена' })
          }
        },
      }
    )
  }, [selectedEvents, getStatistics, openToast])

  const handleClearSelection = useCallback(() => {
    setSelectedEvents([])
    setStatisticsData(null)
  }, [])

  const handleDownloadStatistics = useCallback(() => {
    if (selectedEvents.length === 0) return

    const eventPublicIdList = selectedEvents.map((event) => event.public_id)
    downloadStatistics({ event_public_id_list: eventPublicIdList })
  }, [selectedEvents, downloadStatistics])

  return {
    activeEvents,
    archiveEvents,
    selectedEvents,
    statisticsData,
    activeEventsLoading,
    archiveEventsLoading,
    statisticsLoading,
    downloadLoading,
    statisticsError,
    handleAddEvent,
    handleRemoveEvent,
    handleGetStatistics,
    handleDownloadStatistics,
    handleClearSelection,
  }
}

/**
 * Statistics Tab component for events ticket statistics
 */
const StatisticsTab = () => {
  const {
    activeEvents,
    archiveEvents,
    selectedEvents,
    statisticsData,
    activeEventsLoading,
    archiveEventsLoading,
    statisticsLoading,
    downloadLoading,
    statisticsError,
    handleAddEvent,
    handleRemoveEvent,
    handleGetStatistics,
    handleDownloadStatistics,
    handleClearSelection,
  } = useStatisticsData()

  const isLoading = activeEventsLoading || archiveEventsLoading

  if (isLoading) {
    return <Loader isLoading={true} text="Загрузка событий..." />
  }

  return (
    <Container fluid>
      <Row>
        <Col>
          {/* Event Selection Section */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Выбор событий</h5>
            </Card.Header>
            <Card.Body>
              <EventSelector
                activeEvents={activeEvents}
                archiveEvents={archiveEvents}
                selectedEvents={selectedEvents}
                onAddEvent={handleAddEvent}
                onRemoveEvent={handleRemoveEvent}
              />

              <div className="mt-3">
                <Button
                  variant="primary"
                  onClick={handleGetStatistics}
                  disabled={selectedEvents.length === 0 || statisticsLoading}
                  className="me-2"
                >
                  {statisticsLoading ? 'Загрузка...' : 'Получить статистику'}
                </Button>

                <Button
                  variant="success"
                  onClick={handleDownloadStatistics}
                  disabled={selectedEvents.length === 0 || downloadLoading}
                  className="me-2"
                >
                  {downloadLoading ? 'Скачивание...' : 'Скачать'}
                </Button>

                <Button
                  variant="outline-secondary"
                  onClick={handleClearSelection}
                  disabled={selectedEvents.length === 0}
                >
                  Очистить выбор
                </Button>
              </div>
            </Card.Body>
          </Card>

          {/* Error Display */}
          {statisticsError && (
            <Alert variant="danger" className="mb-4">
              <Alert.Heading>Ошибка при получении статистики</Alert.Heading>
              <p>{statisticsError?.response?.data?.message || 'Произошла ошибка при загрузке данных'}</p>
            </Alert>
          )}

          {/* Statistics Results */}
          {statisticsData && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">Результаты статистики</h5>
              </Card.Header>
              <Card.Body>
                <StatisticsTable data={statisticsData} />
              </Card.Body>
            </Card>
          )}

          {/* Empty State */}
          {!statisticsData && !statisticsLoading && selectedEvents.length > 0 && (
            <Alert variant="info">
              Нажмите &quot;Получить статистику&quot; для загрузки данных по выбранным событиям.
            </Alert>
          )}
        </Col>
      </Row>
    </Container>
  )
}

export default StatisticsTab
