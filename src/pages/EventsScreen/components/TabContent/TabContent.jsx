import { lazy, Suspense } from 'react'

import Loader from '@/components/Loader/Loader'

// Lazy load tab components for better performance
const EventsTab = lazy(() => import('../EventsTab/EventsTab'))
const EventsArchive = lazy(() => import('../EventsArchive/EventsArchive'))
const EventsTypeTab = lazy(() => import('../EventsTypeTab/EventsTypeTab'))
const SportTypeTab = lazy(() => import('../SportTypeTab/SportTypeTab'))

// Component mapping for lazy loaded components
const COMPONENT_MAP = {
  EventsTab,
  EventsArchive,
  EventsTypeTab,
  SportTypeTab,
}

/**
 * Renders a single tab content with suspense boundary
 * @param {string} componentName - Name of the component to render
 * @param {boolean} isActive - Whether this tab is currently active
 * @returns {JSX.Element|null} Rendered component or null
 */
const TabContent = ({ componentName, isActive }) => {
  const Component = COMPONENT_MAP[componentName]

  if (!isActive || !Component) {
    return null
  }

  return (
    <Suspense fallback={<Loader isLoading={true} text="Загрузка..." />}>
      <Component />
    </Suspense>
  )
}

export default TabContent
