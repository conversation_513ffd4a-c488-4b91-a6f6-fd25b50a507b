# NewsScreen

Страница управления новостями с функциями поиска, фильтрации и сортировки.

## Структура

```
NewsScreen/
├── NewsScreen.jsx          # Основной компонент
├── NewsScreen.module.scss  # Стили
├── README.md              # Документация
├── components/            # UI компоненты
│   ├── index.js          # Экспорты компонентов
│   ├── NewsCard.jsx      # Карточка новости
│   ├── EmptyState.jsx    # Пустое состояние
│   └── NewsFilters.jsx   # Фильтры и поиск
└── hooks/                # Кастомные хуки
    ├── index.js          # Экспорты хуков
    ├── useNewsFilters.js # Логика фильтрации
    └── useNewsActions.js # Действия с новостями
```

## Компоненты

### NewsScreen.jsx
Основной компонент страницы. Объединяет все части и управляет общим состоянием.

### NewsCard.jsx
Отвечает за отображение отдельной карточки новости с:
- Изображением (с fallback)
- Статусными бейджами
- Действиями (просмотр, редактирование, удаление)
- Информацией о дате публикации

### NewsFilters.jsx
Панель фильтров содержит:
- Поиск по заголовку, подзаголовку и ограничениям
- Фильтр по статусу (все, опубликованные, черновики, в рассылке)
- Сортировку (по дате, по названию)
- Счетчик результатов

### EmptyState.jsx
Отображается когда нет новостей или не найдены результаты поиска.

## Хуки

### useNewsFilters
Управляет состоянием фильтров и обработкой данных:
- `searchQuery` - текст поиска
- `statusFilter` - выбранный статус
- `sortBy` - выбранная сортировка
- `filteredNews` - отфильтрованный список новостей
- `statusOptions` - опции для фильтра статусов с подсчетом
- `sortOptions` - опции сортировки
- `clearFilters` - функция сброса фильтров

### useNewsActions
Обрабатывает действия с новостями:
- `handleEditNews` - переход к редактированию
- `handleDeleteNews` - удаление с подтверждением
- `handleViewNews` - просмотр в новой вкладке
- `handleCreateNews` - создание новой новости
- `isDeleting` - состояние загрузки при удалении

## Особенности

1. **Модульность**: Каждый компонент имеет свою ответственность
2. **Переиспользование**: Компоненты могут быть легко переиспользованы
3. **Производительность**: Использование useMemo и useCallback для оптимизации
4. **Типобезопасность**: Четкие интерфейсы для props
5. **Accessibility**: Поддержка screen readers и keyboard navigation

## Использование

```jsx
import NewsScreen from './pages/NewsScreen/NewsScreen'

// Компонент готов к использованию
<NewsScreen />
```

## Зависимости

- `@/features/news/api` - API для работы с новостями
- `@/hooks/useToast` - Уведомления
- `@/utils/common` - Общие утилиты
- `@/utils/images` - Работа с изображениями
- `react-bootstrap` - UI компоненты 