import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const downloadEventTicketStatisticsXlsx = (data) => {
  return axios.post(APIRoute.EVENT_TICKET_STATISTICS_XLSX, data, {
    responseType: 'blob',
  })
}

export const useDownloadEventTicketStatisticsXlsx = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: downloadEventTicketStatisticsXlsx,
    onSuccess: (response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'event_ticket_statistics.xlsx')
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      openToast.success({ message: 'Файл статистики билетов успешно скачан' })
    },
  })
}
