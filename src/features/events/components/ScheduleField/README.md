# ScheduleField Component

A refactored, modular component for managing event schedules with date/time handling and timezone support.

## Architecture

The component has been refactored from a monolithic 600+ line component into a modular structure following separation of concerns principles.

### File Structure

```
ScheduleField/
├── components/
│   ├── ScheduleDay.jsx      # Individual day component
│   └── TimeSlot.jsx         # Individual time slot component
├── hooks/
│   ├── useScheduleManager.js    # Core schedule state management
│   └── useScheduleHandlers.js   # Action handlers
├── ScheduleField.jsx        # Main component (orchestrator)
├── index.js                 # Public exports
└── README.md               # This file
```

## Components

### ScheduleField (Main Component)
- **Purpose**: Orchestrates all schedule functionality
- **Responsibilities**: Coordinating hooks and rendering the schedule list
- **Size**: ~40 lines (was 600+ lines)

### ScheduleDay
- **Purpose**: Renders a single schedule day with its time slots
- **Props**: Day data, event handlers, form utilities
- **Responsibilities**: Day-level UI and validation display

### TimeSlot
- **Purpose**: Renders individual time slots within a day
- **Props**: Time slot data, form registration, event handlers
- **Responsibilities**: Time input fields and slot actions

## Custom Hooks

### useScheduleManager
- **Purpose**: Core schedule state and data management
- **Features**:
  - Schedule field array management
  - Unix timestamp to ISO conversion
  - Timezone change handling
  - Change tracking and validation
  - Date/time formatting utilities

### useScheduleHandlers
- **Purpose**: Action handlers for schedule operations
- **Features**:
  - Add/remove schedule days
  - Add/remove time slots
  - Date/time change handling
  - Form value synchronization

## Benefits of Refactoring

1. **Maintainability**: Each piece has a single responsibility
2. **Testability**: Hooks and components can be tested in isolation
3. **Reusability**: Hooks can be reused in other schedule-related components
4. **Readability**: Clear separation between logic and presentation
5. **Performance**: Better optimization opportunities with smaller components

## Usage

```jsx
import { ScheduleField } from './components/ScheduleField'

<ScheduleField
  control={control}
  register={register}
  errors={errors}
  setValue={setValue}
  currentTimezone={currentTimezone}
  onScheduleChange={onScheduleChange}
/>
```

## Key Features

- ✅ Unix timestamp to ISO conversion
- ✅ Timezone-aware date/time handling
- ✅ Form validation and error display
- ✅ Change tracking for unsaved changes
- ✅ Dynamic schedule day and time slot management
- ✅ Proper React Hook dependency management
- ✅ ESLint compliant with no warnings 